{"home": {"hero": {"title": "Game Payment Portal", "subtitle": "The safest way to purchase game credits", "cta": "Browse Games"}, "features": {"title": "Why Choose Us", "security": {"title": "Security First", "description": "Your transactions are protected with industry-leading encryption"}, "instant": {"title": "Instant Transactions", "description": "Get your game credits immediately after payment"}, "payment": {"title": "Multiple Payment Options", "description": "Choose from various payment methods that suit you"}}, "popularGames": "Popular Games", "promo": {"title": "Get 10% Bonus on Your First Purchase", "description": "New users receive an additional 10% game credits on their first transaction", "cta": "Start Now"}}, "ui": {"loading": "Loading...", "noGamesFound": "No Games Found", "gameNotFound": "Game Not Found", "gameNotFoundDesc": "The game you are looking for does not exist or has been removed", "backToHome": "Back to Home", "backToGame": "Back to Game", "continue": "Continue", "close": "Close", "gameIntro": "Game Introduction", "selectRechargeItem": "Select Recharge Item", "selected": "Selected", "pleaseSelect": "Please select a product", "payNow": "Pay Now", "login": "<PERSON><PERSON>", "expand": "Expand", "collapse": "Collapse"}, "game": {"back": "Back to Home", "notFound": "Game not found", "loading": "Loading game details...", "error": "Error loading game", "products": {"title": "Available Products", "empty": "No products available for this game"}, "payment": {"button": "Purchase Now", "title": "Complete Your Purchase", "success": "Payment successful!", "error": "Payment failed. Please try again."}, "list": {"title": "All Games", "subtitle": "Choose from our selection of games to recharge", "view": "View Details"}}, "common": {"loading": "Loading...", "error": "Something went wrong", "retry": "Try Again"}, "nav": {"home": "Home", "games": "Games", "promotions": "Promotions", "support": "Support", "menuOpen": "Open Menu", "menuClose": "Close Menu", "languageSelect": "Language Selection"}, "footer": {"description": "Safe and fast game recharge platform providing the best quality game recharge service.", "aboutUs": "About Us", "companyIntro": "Company Introduction", "joinUs": "Join Us", "contactUs": "Contact Us", "helpCenter": "Help Center", "faq": "FAQ", "paymentGuide": "Payment Guide", "refundPolicy": "Refund Policy", "followUs": "Follow Us", "allRightsReserved": "All rights reserved."}, "about": {"title": "About Us", "subtitle": "Professional Game Payment Solution Provider", "hero": {"title": "Leading the New Era of Game Payments", "subtitle": "We are committed to providing the safest and most convenient game recharge services for global gamers", "description": "As an industry-leading game payment platform, we always adhere to user-centricity and technology innovation as our driving force, building a trustworthy payment bridge for the gaming ecosystem."}, "mission": {"title": "Our Mission", "subtitle": "Making game payments simpler, safer, and more efficient", "values": [{"title": "Security First", "description": "Using bank-level encryption technology to ensure absolute security of every transaction"}, {"title": "User First", "description": "User experience-centered, providing 7×24 professional customer support"}, {"title": "Technology Innovation", "description": "Continuous R&D investment, using latest technology to optimize payment processes"}, {"title": "Integrity Management", "description": "Adhering to transparent and fair business philosophy, building long-term trust relationships"}]}, "stats": {"title": "Our Achievements", "subtitle": "Numbers witness our professionalism and strength", "items": [{"number": "5M+", "label": "Registered Users", "description": "Loyal users worldwide"}, {"number": "1000+", "label": "Partner Games", "description": "Covering mainstream gaming platforms"}, {"number": "99.9%", "label": "System Stability", "description": "7×24 uninterrupted service"}, {"number": "5+", "label": "Years Experience", "description": "Deep expertise in game payments"}]}, "team": {"title": "Professional Team", "subtitle": "Gathering industry elites to create excellent products", "description": "Our team consists of senior engineers, product managers, and operations experts from renowned internet companies, with rich experience in the gaming industry and payment field."}, "technology": {"title": "Technical Advantages", "subtitle": "Advanced technical architecture ensuring service quality", "features": [{"title": "Distributed Architecture", "description": "Microservices architecture ensuring high availability and scalability", "icon": "server"}, {"title": "Intelligent Risk Control", "description": "AI-driven risk control system for real-time transaction security monitoring", "icon": "shield"}, {"title": "Multi-layer Encryption", "description": "End-to-end encrypted transmission protecting user privacy and fund security", "icon": "lock"}, {"title": "Real-time Monitoring", "description": "24-hour system monitoring ensuring stable service operation", "icon": "monitor"}]}, "partners": {"title": "Partners", "subtitle": "Working hand in hand with industry leaders", "description": "We have established deep partnerships with globally renowned game developers and payment institutions to provide quality services for users."}, "contact": {"title": "Contact Us", "subtitle": "Looking forward to establishing partnerships with you", "address": "Room 1502, 15/F, Hong Kong Trade Centre, 117 How Ming Street, Kwun Tong, Kowloon, Hong Kong", "email": "<EMAIL>", "phone": "+86 ************", "businessHours": "Business Hours: Monday to Friday 13:00-21:00", "addressLabel": "Address", "emailLabel": "Email", "phoneLabel": "Phone", "businessHoursLabel": "Business Hours"}}, "login": {"title": "Game Login", "username": "Username", "password": "Password", "usernamePlaceholder": "Enter your username", "passwordPlaceholder": "Enter your password", "loginButton": "<PERSON><PERSON>", "loggingIn": "Logging in...", "close": "Close", "usernameRequired": "Please enter your username", "passwordRequired": "Please enter your password", "invalidCredentials": "<PERSON><PERSON> failed, please check your username and password", "serverError": "<PERSON><PERSON> failed, server response error", "networkError": "<PERSON><PERSON> failed, please check your network connection", "welcome": "Welcome back", "logout": "Logout", "logoutCurrentGame": "<PERSON><PERSON><PERSON> from current game", "logoutAllGames": "<PERSON><PERSON><PERSON> from all games", "loginRequired": "<PERSON><PERSON> Required", "loginToStartRecharge": "Login to start purchasing game items and recharge", "getServerRoleInfoFailed": "Failed to get server role information, please try again", "loggedIn": "Logged In"}, "serverRole": {"title": "Select Server and Role", "selectServer": "Select Server", "selectRole": "Select Role", "pleaseSelectServer": "Please select a server", "pleaseSelectRole": "Please select a role", "level": "Level", "selected": "Selected", "noData": "No server role information available", "pleaseSelectFirst": "Please select server and role first"}, "errors": {"gameIdNotFound": "Game ID not found", "loadGameDataFailed": "Failed to load game data", "pleaseSelectServerRole": "Please select server and role before payment", "serverRoleInfoFailed": "Failed to get server role information, please login again", "allowPopup": "Please allow popup windows to continue payment", "paymentFailed": "Payment failed", "paymentFailedDetail": "Payment failed: {error}\nPlease refresh the page and try again or contact customer service", "fetchPaymentConfigsFailed": "Failed to fetch payment configurations, please refresh the page and try again"}, "payment": {"selectMethod": "Select Payment Method", "selectMethodPlaceholder": "Please select a payment method", "processing": "Processing payment...", "success": "Payment window opened", "successDesc": "Please complete payment in the new window", "failed": "Payment failed", "retry": "Retry", "cancel": "Cancel", "confirm": "Confirm Payment", "pay": "Pay", "allowPopup": "Please allow popups to complete payment", "pleaseSelectMethod": "Please select a payment method", "pleaseSelectServerRole": "Please select server and role first", "missingProductCode": "Missing productCode, please login again", "paymentSuccess": "Payment Successful", "paymentFailed": "Payment Failed", "paymentCancelled": "Payment Cancelled", "paymentCompleted": "Payment Completed", "autoRedirect": "Processing payment result, please wait...", "orderNo": "Order No", "paymentSuccessMessage": "Payment completed successfully, thank you for your purchase!", "paymentFailedMessage": "An error occurred during payment, please try again or contact customer service", "paymentCancelledMessage": "You have cancelled the payment operation", "paymentSuccessSubtitle": "Transaction completed successfully", "paymentFailedSubtitle": "An issue occurred during payment", "paymentCancelledSubtitle": "Payment operation cancelled"}, "customerService": {"title": "Support", "contact": "Contact Support", "help": "Online Support", "support": "Customer Support"}, "help": {"title": "Help Center", "subtitle": "Comprehensive service support for you", "faq": {"title": "Frequently Asked Questions", "subtitle": "Quickly find the answers you need", "items": [{"question": "How to register an account?", "answer": "Click the login button in the upper right corner of the page, enter your username and password in the pop-up login box to complete the login. If you don't have an account yet, please contact customer service to open an account for you."}, {"question": "What payment methods are supported?", "answer": "We support multiple payment methods, including Alipay, WeChat Pay, UnionPay cards, credit cards and other mainstream payment methods. The specific available payment methods will be displayed on the payment page."}, {"question": "How long does it take for recharge to arrive?", "answer": "Most payment methods are credited instantly. If your recharge has not arrived after more than 10 minutes, please contact customer service."}, {"question": "How to view recharge records?", "answer": "After logging in, you can view all recharge records and transaction details in the personal center."}, {"question": "What to do if I forget my password?", "answer": "Please contact customer service to reset your password. Our customer service team will reset your password after verifying your identity information."}, {"question": "What to do if recharge fails?", "answer": "If recharge fails, please check whether the network connection and payment information are correct. If the problem persists, please contact customer service for help."}]}, "paymentGuide": {"title": "Payment Guide", "subtitle": "Detailed payment process instructions", "steps": [{"title": "Select Game", "description": "Choose the game you want to recharge on the homepage and click to enter the game details page.", "icon": "game"}, {"title": "<PERSON><PERSON> Account", "description": "Click the login button and enter your game account information to complete the login.", "icon": "login"}, {"title": "Select Server and Role", "description": "After successful login, select your game server and role information.", "icon": "server"}, {"title": "Select Recharge Product", "description": "Browse available recharge products and select the recharge amount or items you need.", "icon": "product"}, {"title": "Select Payment Method", "description": "Click the payment button and select your preferred payment method in the pop-up payment window.", "icon": "payment"}, {"title": "Complete Payment", "description": "Follow the prompts on the payment page to complete the payment, and the recharge will arrive immediately.", "icon": "success"}], "tips": {"title": "Payment Tips", "items": ["Ensure your network connection is stable to avoid payment interruption", "Please carefully verify server and role information to avoid recharging to wrong account", "Make sure your browser allows pop-up windows when paying", "If you encounter payment problems, please contact customer service for help in time", "It is recommended to use familiar payment methods to ensure payment success"]}}, "refundPolicy": {"title": "Refund Policy", "subtitle": "Understand our refund terms and process", "overview": {"title": "Refund Overview", "description": "We are committed to providing users with quality service experience. Under specific circumstances, we support refund applications. Please read the following refund policy carefully."}, "conditions": {"title": "Refund Conditions", "items": [{"title": "Technical Failure", "description": "Duplicate charges or recharge failures caused by our system technical failures"}, {"title": "Wrong Recharge", "description": "Recharge to wrong game account or server due to system error"}, {"title": "Unreceived Recharge", "description": "Payment successful but recharge not received, and cannot be resolved through technical means"}]}, "process": {"title": "Refund Process", "steps": ["Contact customer service and provide detailed problem description", "Provide relevant payment vouchers and transaction records", "Customer service team will verify your application within 24 hours", "After verification, refund will be processed within 3-7 business days", "Refund will be returned to your payment account via original route"]}, "timeLimit": {"title": "Application Time Limit", "description": "Refund applications must be submitted within 30 days after transaction completion. Applications beyond the time limit will not be accepted."}, "contact": {"title": "Contact Information", "description": "If you need to apply for a refund or have any questions, please contact us through the following methods:", "methods": ["Online Customer Service: Click the customer service button in the lower right corner of the page", "Customer Service Kakao: dovis", "Customer Service Email: <EMAIL>"]}}, "quickLinks": {"title": "Quick Links", "subtitle": "Quick access to common functions and important information", "support247": {"title": "24/7 Customer Support", "description": "Professional customer service team available around the clock"}, "security": {"title": "Security Guarantee", "description": "Bank-level security encryption protects your funds"}, "instant": {"title": "Instant Arrival", "description": "Immediate credit after payment completion, no waiting required"}}, "customerService": {"title": "Online Support", "description": "24-hour professional customer support"}, "notFound": {"title": "Didn't find the answer you need?", "description": "Our professional customer service team is always here to help", "onlineSupport": "Online Support", "sendEmail": "Send Email"}, "paymentHelp": {"title": "Encountering problems during payment?", "description": "Our customer service team is always available to provide professional payment support", "contactSupport": "Contact Support", "callPhone": "Call Phone"}}}